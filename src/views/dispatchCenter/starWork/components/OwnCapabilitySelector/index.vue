<template>
  <el-table 
    v-if="ownPersonData && ownPersonData.length > 0"
    :data="ownPersonData" 
    :empty-text="'暂无数据'" 
    class="resize-table-header-line"
    :cell-style="{ textAlign: 'center' }" 
    :header-cell-style="{ textAlign: 'center' }" 
    border
    style="width: 100%; margin-top: 20px; margin-bottom: 20px"
  >
    <el-table-column 
      v-if="!ecologyType" 
      prop="ownPerson" 
      label="自有能力方" 
    />
    <el-table-column 
      v-if="ecologyType && ecologyType.includes('1')" 
      prop="ownPerson"
      label="自有能力方"
    >
      <template #default="scope">
        <div class="own-person-list">
          <div 
            v-for="(ownPersonItem, index) in scope.row.ownPerson" 
            :key="index" 
            class="box person-wrap"
          >
            <div class="person-selection">
              <a-radio-group :value="selectedValue">
                <a-radio 
                  :value="ownPersonItem.contactPhone"
                  @change="(e) => handleSelectionChange(e, ownPersonItem)"
                  :disabled="isPersonDisabled(ownPersonItem)"
                />
              </a-radio-group>
              <span class="font-weight-500">
                {{ ownPersonItem.belong }}
              </span>
            </div>
            
            <span class="contactName">
              {{ ownPersonItem.contactName }}
            </span>
            
            <p class="contactPhone">
              {{ ownPersonItem.contactPhone }}
            </p>
          </div>
          
          <div v-if="scope.row.ownPerson.length === 0" class="no-data">
            暂无数据
          </div>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'OwnCapabilitySelector',
  props: {
    // 自有能力方数据
    ownPersonData: {
      type: Array,
      default: () => []
    },
    // 生态类型
    ecologyType: {
      type: String,
      default: ''
    },
    // 当前选中的值
    selectedValue: {
      type: String,
      default: ''
    },
    // 被拒绝的公司ID列表
    rejectCompanyIdList: {
      type: Array,
      default: () => []
    }
  },
  emits: ['selection-change'],
  setup(props, { emit }) {
    // 检查人员是否被禁用
    const isPersonDisabled = (person) => {
      return props.rejectCompanyIdList.some(
        (value) => value.userId === person.userId
      )
    }

    // 处理选择变化
    const handleSelectionChange = (e, person) => {
      emit('selection-change', e, person)
    }

    return {
      isPersonDisabled,
      handleSelectionChange
    }
  }
})
</script>

<style lang="scss" scoped>
.own-person-list {
  padding: 12px 0;
}

.box.person-wrap {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.person-selection {
  display: flex;
  width: 200px;
  align-items: center;
  gap: 8px;
}

.font-weight-500 {
  font-weight: 500;
}

.contactName {
  min-width: 100px;
  text-align: left;
}

.contactPhone {
  margin: 0;
  min-width: 120px;
  text-align: left;
  color: #666;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 20px;
}
</style>
