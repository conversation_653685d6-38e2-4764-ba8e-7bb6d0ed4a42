<template>
  <el-table 
    v-if="companyData && companyData.length > 0"
    :data="companyData" 
    :empty-text="'暂无数据'" 
    class="resize-table-header-line"
    :cell-style="{ textAlign: 'center' }" 
    :header-cell-style="{ textAlign: 'center' }" 
    border
    style="width: 100%; margin-top: 20px; margin-bottom: 20px"
  >
    <el-table-column 
      v-if="!ecologyType" 
      prop="company" 
      label="生态厂商" 
    />
    <el-table-column 
      v-if="ecologyType && ecologyType.includes('2')" 
      prop="company"
      label="生态厂商"
    >
      <template #header>
        <div class="table-header">
          <span>生态厂商</span>
          <a-button 
            v-if="showAddButton"
            class="custom_btn active_btn add_btn"
            @click="handleAddPartner"
          >
            新增生态厂商
          </a-button>
        </div>
      </template>
      <template #default="scope">
        <div class="table-row-wrapper">
          <!-- 搜索表单 -->
          <SearchForm
            v-model="searchForm"
            @search="handleSearch"
            @reset="handleReset"
          />
          
          <!-- 生态厂商列表 -->
          <PartnerList
            :company-list="getFilteredCompanyList(scope.row.company)"
            :selected-value="selectedValue"
            :reject-company-id-list="rejectCompanyIdList"
            :contact-select-mode="contactSelectMode"
            @selection-change="handleSelectionChange"
            @contact-change="handleContactChange"
            @company-detail="handleCompanyDetail"
          />
          
          <!-- 分页 -->
          <div class="pagination-wrapper">
            <a-pagination 
              v-model:pageSize="pageSize" 
              v-model:current="currentPage"
              :show-total="total => `共 ${total} 条`" 
              :pageSizeOptions="pageSizeOptions" 
              show-quick-jumper
              show-size-changer 
              :total="getFilteredTotal(scope.row.company)" 
              @change="handlePageChange"
              @showSizeChange="handleSizeChange" 
            />
          </div>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { defineComponent, reactive, computed, ref } from 'vue'
import SearchForm from './SearchForm.vue'
import PartnerList from './PartnerList.vue'

export default defineComponent({
  name: 'EcoPartnerSelector',
  components: {
    SearchForm,
    PartnerList
  },
  props: {
    // 公司数据
    companyData: {
      type: Array,
      default: () => []
    },
    // 生态类型
    ecologyType: {
      type: String,
      default: ''
    },
    // 当前选中的值
    selectedValue: {
      type: String,
      default: ''
    },
    // 被拒绝的公司ID列表
    rejectCompanyIdList: {
      type: Array,
      default: () => []
    },
    // 是否显示新增按钮
    showAddButton: {
      type: Boolean,
      default: true
    },
    // 联系人选择模式
    contactSelectMode: {
      type: String,
      default: 'userId'
    }
  },
  emits: ['selection-change', 'contact-change', 'company-detail', 'add-partner'],
  setup(props, { emit }) {
    // 搜索表单数据
    const searchForm = reactive({
      ecopartnerName: '',
      minScore: '',
      maxScore: ''
    })

    // 搜索状态
    const searchState = reactive({
      isApplied: false,
      cachedResults: null
    })

    // 分页数据
    const currentPage = ref(1)
    const pageSize = ref(5)
    const pageSizeOptions = ref(['5', '10', '20', '30', '50'])

    // 执行搜索逻辑
    const executeSearch = (list) => {
      const { ecopartnerName, minScore, maxScore } = searchForm
      let filteredList = [...list]
      
      // 按企业名称过滤
      if (ecopartnerName && ecopartnerName.trim()) {
        filteredList = filteredList.filter(company =>
          company.ecopartnerName &&
          company.ecopartnerName.includes(ecopartnerName.trim())
        )
      }
      
      // 按评分范围过滤
      if (minScore !== '' || maxScore !== '') {
        filteredList = filteredList.filter(company => {
          const score = company.totalScore || company.introScore || 0
          const min = minScore === '' ? -Infinity : Number(minScore)
          const max = maxScore === '' ? Infinity : Number(maxScore)
          return score >= min && score <= max
        })
      }
      
      return filteredList
    }

    // 获取过滤后的公司列表
    const getFilteredCompanyList = (allList) => {
      if (!allList) return []
      
      // 如果没有应用搜索，直接返回分页结果
      if (!searchState.isApplied) {
        const start = (currentPage.value - 1) * pageSize.value
        const end = start + pageSize.value
        return allList.slice(start, end)
      }
      
      // 检查缓存
      if (searchState.cachedResults) {
        const start = (currentPage.value - 1) * pageSize.value
        const end = start + pageSize.value
        return searchState.cachedResults.slice(start, end)
      }
      
      // 执行搜索并缓存
      const filteredResults = executeSearch(allList)
      searchState.cachedResults = filteredResults
      
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredResults.slice(start, end)
    }

    // 获取过滤后的总数
    const getFilteredTotal = (allList) => {
      if (!allList) return 0
      
      if (!searchState.isApplied) {
        return allList.length
      }
      
      if (searchState.cachedResults) {
        return searchState.cachedResults.length
      }
      
      const filteredResults = executeSearch(allList)
      searchState.cachedResults = filteredResults
      return filteredResults.length
    }

    // 处理搜索
    const handleSearch = () => {
      searchState.isApplied = true
      currentPage.value = 1
      searchState.cachedResults = null
    }

    // 处理重置
    const handleReset = () => {
      searchForm.ecopartnerName = ''
      searchForm.minScore = ''
      searchForm.maxScore = ''
      searchState.isApplied = false
      searchState.cachedResults = null
      currentPage.value = 1
    }

    // 处理分页变化
    const handlePageChange = (page) => {
      currentPage.value = page
    }

    const handleSizeChange = (size) => {
      pageSize.value = size
      currentPage.value = 1
    }

    // 处理选择变化
    const handleSelectionChange = (e, company) => {
      emit('selection-change', e, company)
    }

    // 处理联系人变化
    const handleContactChange = (value, company) => {
      emit('contact-change', value, company)
    }

    // 处理公司详情
    const handleCompanyDetail = (company) => {
      emit('company-detail', company)
    }

    // 处理新增生态厂商
    const handleAddPartner = () => {
      emit('add-partner')
    }

    return {
      searchForm,
      currentPage,
      pageSize,
      pageSizeOptions,
      getFilteredCompanyList,
      getFilteredTotal,
      handleSearch,
      handleReset,
      handlePageChange,
      handleSizeChange,
      handleSelectionChange,
      handleContactChange,
      handleCompanyDetail,
      handleAddPartner
    }
  }
})
</script>

<style lang="scss" scoped>
.table-header {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
}

.table-row-wrapper {
  overflow-x: auto;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.custom_btn {
  padding: 4px 15px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background: #fff;
  cursor: pointer;
  
  &.active_btn {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff;
    
    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
    }
  }
  
  &.add_btn {
    font-size: 12px;
    padding: 2px 8px;
  }
}
</style>
