<template>
  <div class="component-test">
    <h2>组件拆分测试页面</h2>
    
    <div class="test-section">
      <h3>1. 生态厂商选择组件测试</h3>
      <EcoPartnerSelector
        :company-data="mockEcoPartnerData"
        :ecology-type="'2'"
        :selected-value="selectedEcoPartner"
        :reject-company-id-list="[]"
        :contact-select-mode="'userId'"
        @selection-change="handleEcoPartnerSelection"
        @contact-change="handleEcoPartnerContact"
        @company-detail="handleCompanyDetail"
        @add-partner="handleAddPartner"
      />
    </div>

    <div class="test-section">
      <h3>2. 自有能力方选择组件测试</h3>
      <OwnCapabilitySelector
        :own-person-data="mockOwnPersonData"
        :ecology-type="'1'"
        :selected-value="selectedOwnPerson"
        :reject-company-id-list="[]"
        @selection-change="handleOwnPersonSelection"
      />
    </div>

    <div class="test-section">
      <h3>3. 自由联系人选择组件测试</h3>
      <FreeContactSelector
        :contact-data="mockContactData"
        :selected-value="selectedContact"
        :reject-company-id-list="[]"
        :value-field="'belong'"
        @selection-change="handleContactSelection"
      />
    </div>

    <div class="test-results">
      <h3>测试结果</h3>
      <pre>{{ JSON.stringify(testResults, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, ref } from 'vue'
import EcoPartnerSelector from './EcoPartnerSelector/index.vue'
import OwnCapabilitySelector from './OwnCapabilitySelector/index.vue'
import FreeContactSelector from './FreeContactSelector/index.vue'

export default defineComponent({
  name: 'ComponentTest',
  components: {
    EcoPartnerSelector,
    OwnCapabilitySelector,
    FreeContactSelector
  },
  setup() {
    const selectedEcoPartner = ref('')
    const selectedOwnPerson = ref('')
    const selectedContact = ref('')
    
    const testResults = reactive({
      ecoPartnerSelection: null,
      ownPersonSelection: null,
      contactSelection: null,
      events: []
    })

    // 模拟数据
    const mockEcoPartnerData = ref([{
      company: [
        {
          ecopartnerName: '测试生态厂商1',
          contactPhone: '13800138001',
          contactName: '张三',
          auth: 1,
          sync: 1,
          approve: 1,
          totalScore: 85.5,
          contactList: [
            { userId: '1', contactName: '张三', contactPhone: '13800138001', approve: 1 },
            { userId: '2', contactName: '李四', contactPhone: '13800138002', approve: 1 }
          ]
        },
        {
          ecopartnerName: '测试生态厂商2',
          contactPhone: '13800138003',
          contactName: '王五',
          auth: 1,
          sync: 1,
          approve: 1,
          introScore: 92.0,
          contactList: [
            { userId: '3', contactName: '王五', contactPhone: '13800138003', approve: 1 }
          ]
        }
      ]
    }])

    const mockOwnPersonData = ref([{
      ownPerson: [
        {
          belong: '技术部',
          contactName: '内部专家1',
          contactPhone: '13900139001',
          userId: 'own1'
        },
        {
          belong: '产品部',
          contactName: '内部专家2',
          contactPhone: '13900139002',
          userId: 'own2'
        }
      ]
    }])

    const mockContactData = ref([{
      ownProvince: [
        {
          belong: '省级联系人部门1',
          contactName: '省级联系人1',
          contactPhone: '13700137001',
          userId: 'contact1'
        },
        {
          belong: '省级联系人部门2',
          contactName: '省级联系人2',
          contactPhone: '13700137002',
          userId: 'contact2'
        }
      ]
    }])

    // 事件处理函数
    const handleEcoPartnerSelection = (e, company) => {
      selectedEcoPartner.value = e.target.value
      testResults.ecoPartnerSelection = { event: e.target.value, company }
      testResults.events.push(`生态厂商选择: ${company.ecopartnerName}`)
    }

    const handleEcoPartnerContact = (value, company) => {
      testResults.events.push(`生态厂商联系人变更: ${value} - ${company.ecopartnerName}`)
    }

    const handleCompanyDetail = (company) => {
      testResults.events.push(`查看公司详情: ${company.ecopartnerName}`)
    }

    const handleAddPartner = () => {
      testResults.events.push('新增生态厂商')
    }

    const handleOwnPersonSelection = (e, person) => {
      selectedOwnPerson.value = e.target.value
      testResults.ownPersonSelection = { event: e.target.value, person }
      testResults.events.push(`自有能力方选择: ${person.belong} - ${person.contactName}`)
    }

    const handleContactSelection = (e, contact) => {
      selectedContact.value = e.target.value
      testResults.contactSelection = { event: e.target.value, contact }
      testResults.events.push(`自由联系人选择: ${contact.belong} - ${contact.contactName}`)
    }

    return {
      selectedEcoPartner,
      selectedOwnPerson,
      selectedContact,
      testResults,
      mockEcoPartnerData,
      mockOwnPersonData,
      mockContactData,
      handleEcoPartnerSelection,
      handleEcoPartnerContact,
      handleCompanyDetail,
      handleAddPartner,
      handleOwnPersonSelection,
      handleContactSelection
    }
  }
})
</script>

<style lang="scss" scoped>
.component-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.test-results {
  margin-top: 40px;
  padding: 20px;
  background: #f0f0f0;
  border-radius: 8px;
  
  pre {
    background: white;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
  }
}

h2, h3 {
  color: #333;
  margin-bottom: 16px;
}
</style>
