<template>
  <div>
    <!-- 生态厂商表格 -->
    <el-table
      v-if="showEcologyTable && ecologyData?.length > 0"
      :data="tableData"
      :empty-text="'暂无数据'"
      class="resize-table-header-line"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
      border
      style="width: 100%; margin-top: 20px; margin-bottom: 20px"
    >
      <el-table-column
        v-if="!ecologyType"
        prop="company"
        label="生态厂商"
      />
      <el-table-column
        v-if="ecologyType && ecologyType.includes('2')"
        prop="company"
        label="生态厂商"
      >
        <template #header>
          <div style="display: flex; justify-content: center; align-items: center;">
            <span>生态厂商</span>
            <a-button
              v-if="showAddButton"
              class="custom_btn active_btn add_btn"
              @click="handleAddPartner"
            >
              新增生态厂商
            </a-button>
          </div>
        </template>
        <template #default="scope">
          <div class="table-row-wrapper" style="overflow-x: auto;">
            <!-- 搜索区域 -->
            <div
              v-if="showSearch"
              class="search-wrapper"
              style="margin-bottom: 16px;display: flex;flex-direction: row;gap:20px;padding:0 50px;margin-top: 10px;"
            >
              <a-input
                v-model:value="searchForm.ecopartnerName"
                placeholder="请输入生态厂商名称"
                allowClear
                style="width: 240px"
              />
              <div style="background-color: #fff;background-image: none;border: 1px solid #d9d9d9;">
                <a-input-number
                  v-model:value="searchForm.minScore"
                  class="score-input"
                  placeholder="最低生态评分"
                  :min="0"
                  :max="2000"
                  :precision="1"
                  @change="validateScoreRange"
                />
                <span>-</span>
                <a-input-number
                  v-model:value="searchForm.maxScore"
                  class="score-input"
                  placeholder="最高生态评分"
                  :min="0"
                  :max="2000"
                  :precision="1"
                  @change="validateScoreRange"
                />
              </div>
              <div>
                <a-button class="custom_btn active_btn" @click="handleSearchCompany">搜索</a-button>
                <a-button class="custom_btn cancel_btn" style="margin-left: 8px" @click="handleReset">重置</a-button>
              </div>
            </div>

            <!-- 生态厂商列表 -->
            <div
              v-for="(companyItem, index) in filterCompanyList(scope.row.company)"
              :key="index"
              class="box person-wrap"
            >
              <div style="display: flex;width:280px;">
                <a-radio-group :value="selectedId">
                  <a-radio
                    :value="companyItem.contactPhone"
                    @change="(e) => onCheckChange(e, companyItem)"
                    :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)"
                  >
                  </a-radio>
                </a-radio-group>
                <span
                  v-if="companyItem.ecopartnerName"
                  class="company_left company-name font-weight-500"
                  @click="toCompanyDetail(companyItem)"
                >
                  <span class="company_underline">{{ companyItem.ecopartnerName }}</span>
                  <span
                    v-if="companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve && companyItem.approve != 1"
                    style="color: red;font-size:12px;"
                  >
                    生态厂商暂无该生态联系人！
                  </span>
                  <span
                    v-else-if="companyItem.auth == 0"
                    style="color: red;font-size:12px;"
                  >
                    该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！
                  </span>
                  <span
                    v-else-if="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve)"
                    style="color: red;font-size:12px;"
                  >
                    生态厂商暂无该生态联系人！
                  </span>
                </span>
              </div>
              <div class="company_right" style="display: inline-flex; align-items: center; ">
                <img width="20px" height="20px" style="margin-bottom: 0;" src="@/assets/images/score.png" />
                <span>
                  生态评分：
                  <span style="color: #FF9C39FF;font-weight: bold;">
                    {{ formatScore(companyItem) }}
                  </span>
                </span>
              </div>
              <a-select
                v-model:value="companyItem.contactName"
                @change="(value) => selectUserCom(value, companyItem)"
                :disabled="!(companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1)"
              >
                <template v-for="(opt, index) in companyItem.contactList" :key="index">
                  <a-select-option
                    :value="opt.userId"
                    :label="opt.contactName"
                    :disabled="opt.approve != 1"
                  >
                    {{ opt.contactName }}
                  </a-select-option>
                </template>
              </a-select>
              <span
                :style="{ color: companyItem.auth == 1 && companyItem.sync == 1 && companyItem.approve == 1 ? '' : '#999' }"
              >
                {{ companyItem.contactPhone }}
              </span>
            </div>
            <div v-if="filterCompanyList(scope.row.company).length == 0">暂无数据</div>
          </div>
          <!-- 分页 -->
          <div v-if="showPagination" style="margin-top:20px;text-align: right;">
            <a-pagination
              v-model:pageSize="pageItemSize"
              v-model:current="currentPage"
              :show-total="total => `共 ${getFilteredTotal(scope.row.company)} 条`"
              :pageSizeOptions="pageSizeOptions"
              show-quick-jumper
              show-size-changer
              :total="getFilteredTotal(scope.row.company)"
              @change="pageChange"
              @showSizeChange="sizeChange"
            />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 自有能力方表格 -->
    <el-table
      v-if="showOwnPersonTable && ownPersonData?.length > 0"
      :data="tableData"
      :empty-text="'暂无数据'"
      class="resize-table-header-line"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
      border
      style="width: 100%; margin-top: 20px; margin-bottom: 20px"
    >
      <el-table-column
        v-if="!ecologyType"
        prop="ownPerson"
        label="自有能力方"
      />
      <el-table-column
        v-if="ecologyType && ecologyType.includes('1')"
        prop="ownPerson"
        label="自有能力方"
      >
        <template #default="scope">
          <div v-for="(ownPersonItem, index) in scope.row.ownPerson" :key="index" class="box person-wrap">
            <div style="display: flex;width:200px;">
              <a-radio-group :value="selectedIdOwn">
                <a-radio
                  :value="ownPersonItem.contactPhone"
                  @change="(e) => onCheckChange(e, ownPersonItem)"
                >
                </a-radio>
              </a-radio-group>
              <span class="font-weight-500">
                {{ ownPersonItem.belong }}
              </span>
            </div>
            <span class="contactName">
              {{ ownPersonItem.contactName }}
            </span>
            <p class="contactPhone">
              {{ ownPersonItem.contactPhone }}
            </p>
          </div>
          <div v-if="scope.row.ownPerson.length == 0">暂无数据</div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 自有联系人表格 -->
    <el-table
      v-if="showOwnProvinceTable && ownProvinceData?.length > 0"
      :data="tableData"
      :empty-text="'暂无数据'"
      class="resize-table-header-line"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
      border
      style="width: 100%; margin-top: 20px; margin-bottom: 20px"
    >
      <el-table-column prop="ownProvince" label="自有联系人">
        <template #default="scope">
          <div v-for="(ownProvinceItem, index) in scope.row.ownProvince" :key="index" class="box person-wrap">
            <div style="display: flex;width:200px;">
              <a-radio-group :value="selectedIdOwn">
                <a-radio
                  :value="ownProvinceItem.belong"
                  @change="(e) => onCheckChange(e, ownProvinceItem)"
                >
                </a-radio>
              </a-radio-group>
              <span class="font-weight-500">
                {{ ownProvinceItem.belong }}
              </span>
            </div>
            <span class="contactName">
              {{ ownProvinceItem.contactName }}
            </span>
            <p class="contactPhone">
              {{ ownProvinceItem.contactPhone }}
            </p>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue'

export default defineComponent({
  name: 'PartnerTable',
  props: {
    ecologyData: { type: Array, default: () => [] },
    ownPersonData: { type: Array, default: () => [] },
    ownProvinceData: { type: Array, default: () => [] },
    ecologyType: { type: String, default: '' },
    selectedId: { type: String, default: '' },
    selectedIdOwn: { type: String, default: '' },
    searchForm: { type: Object, default: () => ({ ecopartnerName: '', minScore: '', maxScore: '' }) },
    currentPage: { type: Number, default: 1 },
    pageItemSize: { type: Number, default: 5 },
    pageSizeOptions: { type: Array, default: () => ['5', '10', '20', '30', '50'] },
    showSearch: { type: Boolean, default: true },
    showPagination: { type: Boolean, default: true },
    showAddButton: { type: Boolean, default: false },
    rejectCompanyIdlist: { type: Array, default: () => [] }
  },
  emits: ['check-change', 'add-partner', 'search-company', 'reset-search', 'page-change', 'size-change', 'company-detail', 'select-user'],
  setup(props, { emit }) {
    const showEcologyTable = computed(() => props.ecologyData && props.ecologyData.length > 0)
    const showOwnPersonTable = computed(() => props.ownPersonData && props.ownPersonData.length > 0)
    const showOwnProvinceTable = computed(() => props.ownProvinceData && props.ownProvinceData.length > 0)

    const tableData = computed(() => {
      if (showEcologyTable.value) return [{ company: props.ecologyData }]
      if (showOwnPersonTable.value) return [{ ownPerson: props.ownPersonData }]
      if (showOwnProvinceTable.value) return [{ ownProvince: props.ownProvinceData }]
      return []
    })

    const filterCompanyList = (companyList) => {
      if (!companyList) return []
      let filtered = [...companyList]
      if (props.searchForm.ecopartnerName) {
        filtered = filtered.filter(item =>
          item.ecopartnerName &&
          item.ecopartnerName.toLowerCase().includes(props.searchForm.ecopartnerName.toLowerCase())
        )
      }
      if (props.searchForm.minScore !== '' && props.searchForm.maxScore !== '') {
        filtered = filtered.filter(item => {
          const score = formatScore(item)
          return score >= props.searchForm.minScore && score <= props.searchForm.maxScore
        })
      }
      return filtered
    }

    const getFilteredTotal = (companyList) => filterCompanyList(companyList).length

    const formatScore = (item) => {
      if (!item || !item.score) return 0
      return parseFloat(item.score) || 0
    }

    const onCheckChange = (e, item) => emit('check-change', e, item)
    const handleAddPartner = () => emit('add-partner')
    const handleSearchCompany = () => emit('search-company')
    const handleReset = () => emit('reset-search')
    const pageChange = (page) => emit('page-change', page)
    const sizeChange = (current, size) => emit('size-change', current, size)
    const toCompanyDetail = (item) => emit('company-detail', item)
    const selectUserCom = (value, item) => emit('select-user', value, item)
    const validateScoreRange = () => {
      const { minScore, maxScore } = props.searchForm
      if (minScore !== '' && maxScore !== '' && minScore > maxScore) {
        console.warn('最低评分不能大于最高评分')
      }
    }

    return {
      showEcologyTable,
      showOwnPersonTable,
      showOwnProvinceTable,
      tableData,
      filterCompanyList,
      getFilteredTotal,
      formatScore,
      onCheckChange,
      handleAddPartner,
      handleSearchCompany,
      handleReset,
      pageChange,
      sizeChange,
      toCompanyDetail,
      selectUserCom,
      validateScoreRange
    }
  }
})
</script>

<style scoped>
.box {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
}

.person-wrap {
  display: flex;
  align-items: center;
  gap: 12px;
}

.company_left {
  flex: 1;
}

.company_underline {
  text-decoration: underline;
  cursor: pointer;
  color: #1890ff;
}

.company_right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.contactName {
  font-weight: 500;
  color: #333;
}

.contactPhone {
  color: #666;
  margin: 0;
}

.font-weight-500 {
  font-weight: 500;
}

.search-wrapper {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
}

.score-input {
  width: 100px;
}

.add_btn {
  margin-left: 8px;
}
</style>
